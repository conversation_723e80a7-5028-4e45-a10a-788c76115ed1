'use server';

import { revalidatePath } from 'next/cache';
import { prisma, withRetry } from '@/lib/db';
import { getCurrentDbUser, getUserSSOProvider } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { headers } from 'next/headers';
import {
  profileUpdateSchema,
  passwordChangeSchema,
  notificationPreferencesSchema,
  securitySettingsSchema,
  type ProfileUpdateInput,
  type PasswordChangeInput,
  type NotificationPreferencesInput,
  type SecuritySettingsInput
} from '@/lib/zod';
import { z } from 'zod';
import { currentUser } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';

export interface ActionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  fieldErrors?: Record<string, string[]>;
}

/**
 * Updates user profile information
 */
export async function updateProfile(input: ProfileUpdateInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = profileUpdateSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Check if user is SSO and trying to change email
    const ssoProvider = await getUserSSOProvider();
    if (ssoProvider && validatedData.email !== dbUser.email) {
      return {
        success: false,
        error: `Nu puteți schimba email-ul pentru că sunteți conectat prin ${ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}. Email-ul este gestionat de furnizorul SSO.`
      };
    }

    // Check if email is being changed and if it's already taken
    if (validatedData.email !== dbUser.email) {
      const existingUser = await withRetry(() =>
        prisma.user.findUnique({
          where: { email: validatedData.email }
        })
      );

      if (existingUser && existingUser.id !== dbUser.id) {
        return {
          success: false,
          error: 'Această adresă de email este deja folosită de alt utilizator'
        };
      }
    }

    // Update user in database
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          email: validatedData.email,
          phoneNumber: validatedData.phoneNumber,
          bio: validatedData.bio,
          jobTitle: validatedData.jobTitle,
          department: validatedData.department,
          salutation: validatedData.salutation,
          preferredLanguage: validatedData.preferredLanguage,
          timezone: validatedData.timezone,
          updatedAt: new Date(),
        }
      })
    );

    // Update Clerk user if email or name changed
    try {
      const clerkUser = await currentUser();
      const clerk = await clerkClient();
      if (clerkUser && (
        validatedData.email !== dbUser.email ||
        validatedData.firstName !== dbUser.firstName ||
        validatedData.lastName !== dbUser.lastName
      )) {
        await clerk.users.updateUser(clerkUser.id, {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          primaryEmailAddressID: validatedData.email !== dbUser.email ? undefined : clerkUser.primaryEmailAddressId || undefined,
        });
      }
    } catch (clerkError) {
      logger.warn(`[updateProfile] Failed to update Clerk user: ${clerkError}`);
      // Don't fail the entire operation if Clerk update fails
    }

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'profile.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              firstName: dbUser.firstName,
              lastName: dbUser.lastName,
              email: dbUser.email,
              phoneNumber: dbUser.phoneNumber,
              bio: dbUser.bio,
              jobTitle: dbUser.jobTitle,
              department: dbUser.department,
              salutation: dbUser.salutation,
              preferredLanguage: dbUser.preferredLanguage,
              timezone: dbUser.timezone,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');
    revalidatePath('/profile');

    logger.info(`[updateProfile] Profile updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateProfile] Error updating profile:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la actualizarea profilului. Vă rugăm să încercați din nou.' 
    };
  }
}

/**
 * Updates notification preferences
 */
export async function updateNotificationPreferences(input: NotificationPreferencesInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = notificationPreferencesSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Update user preferences
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          emailNotifications: validatedData.emailNotifications,
          pushNotifications: validatedData.pushNotifications,
          smsNotifications: validatedData.smsNotifications,
          newsletterOptIn: validatedData.newsletterOptIn,
          updatedAt: new Date(),
        }
      })
    );

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'preferences.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              emailNotifications: dbUser.emailNotifications,
              pushNotifications: dbUser.pushNotifications,
              smsNotifications: dbUser.smsNotifications,
              newsletterOptIn: dbUser.newsletterOptIn,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');

    logger.info(`[updateNotificationPreferences] Preferences updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateNotificationPreferences] Error updating preferences:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la actualizarea preferințelor. Vă rugăm să încercați din nou.' 
    };
  }
}

/**
 * Updates security settings (like 2FA)
 */
export async function updateSecuritySettings(input: SecuritySettingsInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = securitySettingsSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Update security settings
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          twoFactorEnabled: validatedData.twoFactorEnabled,
          updatedAt: new Date(),
        }
      })
    );

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'security.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              twoFactorEnabled: dbUser.twoFactorEnabled,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');

    logger.info(`[updateSecuritySettings] Security settings updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateSecuritySettings] Error updating security settings:', error);
    return {
      success: false,
      error: 'A apărut o eroare la actualizarea setărilor de securitate. Vă rugăm să încercați din nou.'
    };
  }
}

/**
 * Changes user password (for Clerk-managed authentication)
 * Note: This is a placeholder - actual password change should be handled by Clerk
 */
export async function changePassword(input: PasswordChangeInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = passwordChangeSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Since we're using Clerk for authentication, password changes should be handled through Clerk
    // This is a placeholder for the actual implementation
    // In a real implementation, you would:
    // 1. Verify the current password with Clerk
    // 2. Update the password through Clerk's API
    // 3. Update the passwordChangedAt field in your database

    try {
      const clerkUser = await currentUser();
      if (!clerkUser) {
        return { success: false, error: 'Utilizatorul nu a fost găsit în sistemul de autentificare' };
      }

      // For now, we'll just update the passwordChangedAt field
      // In production, you should implement actual password verification and update through Clerk
      await withRetry(() =>
        prisma.user.update({
          where: { id: dbUser.id },
          data: {
            passwordChangedAt: new Date(),
            updatedAt: new Date(),
          }
        })
      );

      // Create audit log
      await withRetry(() =>
        prisma.userAuditLog.create({
          data: {
            userId: dbUser.id,
            action: 'password.change',
            entityType: 'user',
            entityId: dbUser.id,
            details: JSON.stringify({
              message: 'Password change requested',
              // Don't log actual passwords for security
            }),
            ipAddress: headerPayload.get('x-forwarded-for') || null,
            userAgent: headerPayload.get('user-agent') || null,
          }
        })
      );

      logger.info(`[changePassword] Password change logged for user: ${dbUser.id}`);

      return {
        success: true,
        data: { message: 'Parola a fost actualizată cu succes' },
        error: undefined
      };

    } catch (clerkError) {
      logger.error(`[changePassword] Clerk error: ${clerkError}`);
      return {
        success: false,
        error: 'A apărut o eroare la actualizarea parolei. Vă rugăm să încercați din nou.'
      };
    }

  } catch (error) {
    logger.error('[changePassword] Error changing password:', error);
    return {
      success: false,
      error: 'A apărut o eroare la schimbarea parolei. Vă rugăm să încercați din nou.'
    };
  }
}

// Account management schemas
const suspendAccountSchema = z.object({
  reason: z.string().min(1, 'Motivul este obligatoriu').max(500, 'Motivul este prea lung')
});

const deleteAccountSchema = z.object({
  reason: z.string().min(1, 'Motivul este obligatoriu').max(500, 'Motivul este prea lung'),
  confirmationText: z.literal('ȘTERGE CONTUL MEU', {
    errorMap: () => ({ message: 'Textul de confirmare nu este corect' })
  })
});

type SuspendAccountInput = z.infer<typeof suspendAccountSchema>;
type DeleteAccountInput = z.infer<typeof deleteAccountSchema>;

/**
 * Suspends a user account temporarily
 */
export async function suspendAccount(input: SuspendAccountInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = suspendAccountSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Update user status to suspended
    await withRetry(() =>
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          isSuspended: true,
          suspensionReason: validatedData.reason,
          updatedAt: new Date(),
        }
      })
    );

    // Create audit log
    await withRetry(() =>
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'account.suspend',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            reason: validatedData.reason,
            suspendedAt: new Date().toISOString(),
            suspendedBy: dbUser.id
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Disable user in Clerk (optional - keeps them logged out)
    try {
      const clerkUser = await currentUser();
      if (clerkUser) {
        const client = await clerkClient();
        await client.users.updateUser(clerkUser.id, {
          publicMetadata: {
            ...clerkUser.publicMetadata,
            suspended: true,
            suspendedAt: new Date().toISOString(),
            suspendedReason: validatedData.reason,
            suspendedBy: dbUser.id
          }
        });
      }
    } catch (clerkError) {
      logger.warn(`[suspendAccount] Failed to update Clerk user metadata: ${String(clerkError)}`);
      // Continue anyway - database suspension is more important
    }

    logger.info(`[suspendAccount] Account suspended successfully for user: ${dbUser.id}`);

    // Revalidate relevant paths
    revalidatePath('/account/settings');

    return { success: true };

  } catch (error) {
    logger.error('[suspendAccount] Error suspending account:', error);
    return {
      success: false,
      error: 'A apărut o eroare la suspendarea contului. Vă rugăm să încercați din nou.'
    };
  }
}

/**
 * Permanently deletes a user account and all associated data
 */
export async function deleteAccount(input: DeleteAccountInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = deleteAccountSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Create final audit log before deletion
    await withRetry(() =>
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'account.delete',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            reason: validatedData.reason,
            confirmationText: validatedData.confirmationText,
            deletedAt: new Date().toISOString(),
            email: dbUser.email // Keep for audit purposes
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Soft delete user (set deletedAt instead of hard delete to preserve audit logs)
    await withRetry(() =>
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          deletedAt: new Date(),
          isActive: false,
          isSuspended: true,
          // Anonymize personal data
          email: `deleted_${dbUser.id}@deleted.local`,
          firstName: 'Deleted',
          lastName: 'User',
          phoneNumber: '',
          bio: '',
          profileImage: '',
          updatedAt: new Date(),
        }
      })
    );

    // Delete user from Clerk
    try {
      const clerkUser = await currentUser();
      if (clerkUser) {
        const client = await clerkClient();
        await client.users.deleteUser(clerkUser.id);
      }
    } catch (clerkError) {
      logger.warn(`[deleteAccount] Failed to delete Clerk user: ${String(clerkError)}`);
      // Continue anyway - database deletion is more important
    }

    logger.info(`[deleteAccount] Account deleted successfully for user: ${dbUser.id}`);

    return { success: true };

  } catch (error) {
    logger.error('[deleteAccount] Error deleting account:', error);
    return {
      success: false,
      error: 'A apărut o eroare la ștergerea contului. Vă rugăm să încercați din nou.'
    };
  }
}
