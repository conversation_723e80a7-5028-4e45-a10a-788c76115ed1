'use server';

import { revalidatePath } from 'next/cache';
import { prisma, withRetry } from '@/lib/db';
import { getCurrentDbUser, getUserSSOProvider } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { headers } from 'next/headers';
import { 
  profileUpdateSchema, 
  passwordChangeSchema, 
  notificationPreferencesSchema, 
  securitySettingsSchema,
  type ProfileUpdateInput,
  type PasswordChangeInput,
  type NotificationPreferencesInput,
  type SecuritySettingsInput
} from '@/lib/zod';
import { currentUser } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';

export interface ActionResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  fieldErrors?: Record<string, string[]>;
}

/**
 * Updates user profile information
 */
export async function updateProfile(input: ProfileUpdateInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = profileUpdateSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Check if user is SSO and trying to change email
    const ssoProvider = await getUserSSOProvider();
    if (ssoProvider && validatedData.email !== dbUser.email) {
      return {
        success: false,
        error: `Nu puteți schimba email-ul pentru că sunteți conectat prin ${ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}. Email-ul este gestionat de furnizorul SSO.`
      };
    }

    // Check if email is being changed and if it's already taken
    if (validatedData.email !== dbUser.email) {
      const existingUser = await withRetry(() =>
        prisma.user.findUnique({
          where: { email: validatedData.email }
        })
      );

      if (existingUser && existingUser.id !== dbUser.id) {
        return {
          success: false,
          error: 'Această adresă de email este deja folosită de alt utilizator'
        };
      }
    }

    // Update user in database
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          email: validatedData.email,
          phoneNumber: validatedData.phoneNumber,
          bio: validatedData.bio,
          jobTitle: validatedData.jobTitle,
          department: validatedData.department,
          salutation: validatedData.salutation,
          preferredLanguage: validatedData.preferredLanguage,
          timezone: validatedData.timezone,
          updatedAt: new Date(),
        }
      })
    );

    // Update Clerk user if email or name changed
    try {
      const clerkUser = await currentUser();
      const clerk = await clerkClient();
      if (clerkUser && (
        validatedData.email !== dbUser.email ||
        validatedData.firstName !== dbUser.firstName ||
        validatedData.lastName !== dbUser.lastName
      )) {
        await clerk.users.updateUser(clerkUser.id, {
          firstName: validatedData.firstName,
          lastName: validatedData.lastName,
          primaryEmailAddressID: validatedData.email !== dbUser.email ? undefined : clerkUser.primaryEmailAddressId || undefined,
        });
      }
    } catch (clerkError) {
      logger.warn(`[updateProfile] Failed to update Clerk user: ${clerkError}`);
      // Don't fail the entire operation if Clerk update fails
    }

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'profile.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              firstName: dbUser.firstName,
              lastName: dbUser.lastName,
              email: dbUser.email,
              phoneNumber: dbUser.phoneNumber,
              bio: dbUser.bio,
              jobTitle: dbUser.jobTitle,
              department: dbUser.department,
              salutation: dbUser.salutation,
              preferredLanguage: dbUser.preferredLanguage,
              timezone: dbUser.timezone,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');
    revalidatePath('/profile');

    logger.info(`[updateProfile] Profile updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateProfile] Error updating profile:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la actualizarea profilului. Vă rugăm să încercați din nou.' 
    };
  }
}

/**
 * Updates notification preferences
 */
export async function updateNotificationPreferences(input: NotificationPreferencesInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = notificationPreferencesSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Update user preferences
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          emailNotifications: validatedData.emailNotifications,
          pushNotifications: validatedData.pushNotifications,
          smsNotifications: validatedData.smsNotifications,
          newsletterOptIn: validatedData.newsletterOptIn,
          updatedAt: new Date(),
        }
      })
    );

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'preferences.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              emailNotifications: dbUser.emailNotifications,
              pushNotifications: dbUser.pushNotifications,
              smsNotifications: dbUser.smsNotifications,
              newsletterOptIn: dbUser.newsletterOptIn,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');

    logger.info(`[updateNotificationPreferences] Preferences updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateNotificationPreferences] Error updating preferences:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la actualizarea preferințelor. Vă rugăm să încercați din nou.' 
    };
  }
}

/**
 * Updates security settings (like 2FA)
 */
export async function updateSecuritySettings(input: SecuritySettingsInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = securitySettingsSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Update security settings
    const updatedUser = await withRetry(() => 
      prisma.user.update({
        where: { id: dbUser.id },
        data: {
          twoFactorEnabled: validatedData.twoFactorEnabled,
          updatedAt: new Date(),
        }
      })
    );

    // Create audit log
    await withRetry(() => 
      prisma.userAuditLog.create({
        data: {
          userId: dbUser.id,
          action: 'security.update',
          entityType: 'user',
          entityId: dbUser.id,
          details: JSON.stringify({
            changes: validatedData,
            previousData: {
              twoFactorEnabled: dbUser.twoFactorEnabled,
            }
          }),
          ipAddress: headerPayload.get('x-forwarded-for') || null,
          userAgent: headerPayload.get('user-agent') || null,
        }
      })
    );

    // Revalidate relevant paths
    revalidatePath('/account/settings');

    logger.info(`[updateSecuritySettings] Security settings updated successfully for user: ${dbUser.id}`);
    
    return { 
      success: true, 
      data: updatedUser,
      error: undefined 
    };

  } catch (error) {
    logger.error('[updateSecuritySettings] Error updating security settings:', error);
    return {
      success: false,
      error: 'A apărut o eroare la actualizarea setărilor de securitate. Vă rugăm să încercați din nou.'
    };
  }
}

/**
 * Changes user password (for Clerk-managed authentication)
 * Note: This is a placeholder - actual password change should be handled by Clerk
 */
export async function changePassword(input: PasswordChangeInput): Promise<ActionResult> {
  try {
    // Get current user
    const dbUser = await getCurrentDbUser();
    if (!dbUser) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = passwordChangeSchema.safeParse(input);
    if (!validationResult.success) {
      return {
        success: false,
        error: 'Datele introduse nu sunt valide',
        fieldErrors: validationResult.error.flatten().fieldErrors
      };
    }

    const validatedData = validationResult.data;
    const headerPayload = await headers();

    // Since we're using Clerk for authentication, password changes should be handled through Clerk
    // This is a placeholder for the actual implementation
    // In a real implementation, you would:
    // 1. Verify the current password with Clerk
    // 2. Update the password through Clerk's API
    // 3. Update the passwordChangedAt field in your database

    try {
      const clerkUser = await currentUser();
      if (!clerkUser) {
        return { success: false, error: 'Utilizatorul nu a fost găsit în sistemul de autentificare' };
      }

      // For now, we'll just update the passwordChangedAt field
      // In production, you should implement actual password verification and update through Clerk
      await withRetry(() =>
        prisma.user.update({
          where: { id: dbUser.id },
          data: {
            passwordChangedAt: new Date(),
            updatedAt: new Date(),
          }
        })
      );

      // Create audit log
      await withRetry(() =>
        prisma.userAuditLog.create({
          data: {
            userId: dbUser.id,
            action: 'password.change',
            entityType: 'user',
            entityId: dbUser.id,
            details: JSON.stringify({
              message: 'Password change requested',
              // Don't log actual passwords for security
            }),
            ipAddress: headerPayload.get('x-forwarded-for') || null,
            userAgent: headerPayload.get('user-agent') || null,
          }
        })
      );

      logger.info(`[changePassword] Password change logged for user: ${dbUser.id}`);

      return {
        success: true,
        data: { message: 'Parola a fost actualizată cu succes' },
        error: undefined
      };

    } catch (clerkError) {
      logger.error(`[changePassword] Clerk error: ${clerkError}`);
      return {
        success: false,
        error: 'A apărut o eroare la actualizarea parolei. Vă rugăm să încercați din nou.'
      };
    }

  } catch (error) {
    logger.error('[changePassword] Error changing password:', error);
    return {
      success: false,
      error: 'A apărut o eroare la schimbarea parolei. Vă rugăm să încercați din nou.'
    };
  }
}
