"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ProfileForm from "./ProfileForm";
import PasswordForm from "./PasswordForm";
import PreferencesForm from "./PreferencesForm";
import SecuritySettings from "./SecuritySettings";
import { AccountSettingsData, UserSecurityInfo, UserAuditLogEntry } from "@/app/getData/account-settings";

interface AccountPageProps {
  accountData: AccountSettingsData;
  securityInfo: UserSecurityInfo | null;
  auditLogs: UserAuditLogEntry[];
}

export default function AccountPage({ accountData, securityInfo, auditLogs }: AccountPageProps) {
  return (
    <div className="space-y-6">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight"><PERSON><PERSON><PERSON></h1>
          <p className="text-muted-foreground">
            Gestionați informațiile contului și preferințele de securitate
          </p>
        </div>
      </header>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile">Profil</TabsTrigger>
          <TabsTrigger value="password">Parolă</TabsTrigger>
          <TabsTrigger value="preferences">Preferințe</TabsTrigger>
          <TabsTrigger value="security">Securitate</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <ProfileForm accountData={accountData} />
        </TabsContent>

        <TabsContent value="password" className="space-y-6">
          <PasswordForm lastPasswordChange={securityInfo?.passwordChangedAt} />
        </TabsContent>

        <TabsContent value="preferences" className="space-y-6">
          <PreferencesForm accountData={accountData} />
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <SecuritySettings securityInfo={securityInfo} auditLogs={auditLogs} />
        </TabsContent>
      </Tabs>
    </div>
  );
}