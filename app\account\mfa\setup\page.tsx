'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Smartphone, Shield, CheckCircle, AlertCircle } from "lucide-react";

export default function MFASetupPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isEnabling, setIsEnabling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (isLoaded && !user) {
      router.push('/sign-in');
    }
  }, [isLoaded, user, router]);

  const handleEnableMFA = async () => {
    if (!user) return;

    setIsEnabling(true);
    setError(null);

    try {
      // Create a TOTP (Time-based One-Time Password) factor
      const totpFactor = await user.createTOTP();
      
      // This will trigger Clerk's built-in MFA setup flow
      // The user will see a QR code to scan with their authenticator app
      await totpFactor.prepareVerification();
      
      setSuccess(true);
      
      // Redirect back to security settings after a delay
      setTimeout(() => {
        window.close(); // Close the popup window
        // Or redirect to security settings if not in popup
        router.push('/account/settings?tab=security');
      }, 3000);
      
    } catch (err: any) {
      console.error('MFA setup error:', err);
      setError(err.message || 'A apărut o eroare la configurarea 2FA. Vă rugăm să încercați din nou.');
    } finally {
      setIsEnabling(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
            <Shield className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle>Configurare Autentificare 2FA</CardTitle>
          <CardDescription>
            Adăugați un nivel suplimentar de securitate la contul dvs.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                2FA a fost configurat cu succes! Veți fi redirecționat înapoi la setări.
              </AlertDescription>
            </Alert>
          )}

          {!success && (
            <>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">Aplicație de autentificare</p>
                    <p className="text-sm text-muted-foreground">
                      Veți avea nevoie de o aplicație precum Google Authenticator sau Authy
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Cum funcționează:</h4>
                <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                  <li>Scanați codul QR cu aplicația de autentificare</li>
                  <li>Introduceți codul generat pentru verificare</li>
                  <li>2FA va fi activat pe contul dvs.</li>
                </ol>
              </div>

              <Button 
                onClick={handleEnableMFA}
                disabled={isEnabling}
                className="w-full"
              >
                {isEnabling ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Se configurează...
                  </>
                ) : (
                  'Începe configurarea 2FA'
                )}
              </Button>
            </>
          )}

          <Button 
            variant="outline" 
            onClick={() => {
              window.close();
              router.push('/account/settings?tab=security');
            }}
            className="w-full"
          >
            Anulează
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
