"use client"

import {
  ShieldX,
  Mail,
  Phone,
  ArrowLeft,
  AlertCircle,
  FileText,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";


export default function AccountSuspendedPage() {
  const navigate = useRouter().push;

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-slate-50 flex items-center justify-center px-4">
      <div className="max-w-4xl mx-auto text-center">
        {/* Warning Icon */}
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-red-100 rounded-full mb-6 animate-pulse">
            <ShieldX className="w-12 h-12 text-red-600" />
          </div>
        </div>

        {/* Status Message */}
        <div className="mb-12 space-y-4">
          <Badge className="bg-red-100 text-red-800 px-4 py-2 text-sm font-semibold mb-4">
            Account Suspended
          </Badge>
          <h1 className="text-4xl md:text-5xl font-bold text-[#4D4D4D] mb-4">
            Account Suspended
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto leading-relaxed">
            Your BMW parts account has been temporarily suspended due to
            security concerns or policy violations.
          </p>
        </div>

        {/* Information Card */}
        <Card className="max-w-2xl mx-auto mb-8 shadow-xl border-0 bg-white">
          <CardContent className="p-8">
            <div className="space-y-6">
              <div className="flex items-center justify-center gap-3 mb-6">
                <AlertCircle className="w-6 h-6 text-red-600" />
                <h3 className="text-2xl font-bold text-[#4D4D4D]">
                  Suspension Details
                </h3>
              </div>

              <div className="text-left space-y-4">
                <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    Common reasons for suspension:
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Suspicious account activity detected</li>
                    <li>• Multiple failed login attempts</li>
                    <li>• Violation of terms of service</li>
                    <li>• Fraudulent payment activity</li>
                    <li>• Security verification required</li>
                  </ul>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    To restore your account:
                  </h4>
                  <ul className="text-sm text-slate-600 space-y-1">
                    <li>• Contact our security team immediately</li>
                    <li>• Provide identity verification documents</li>
                    <li>• Review and acknowledge our terms of service</li>
                    <li>• Complete security verification process</li>
                    <li>• Wait for account review (24-48 hours)</li>
                  </ul>
                </div>

                <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                  <h4 className="font-semibold text-[#4D4D4D] mb-2">
                    Important Notice:
                  </h4>
                  <p className="text-sm text-slate-600">
                    During suspension, you cannot place orders, access account
                    information, or use premium features. Existing orders will
                    continue to be processed normally.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
          <Button
            onClick={() =>
              (window.location.href =
                "mailto:<EMAIL>?subject=Account Suspension Appeal")
            }
            className="bg-[#0066B1] hover:bg-[#0052A3] text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Mail className="w-5 h-5 mr-2" />
            Appeal Suspension
          </Button>

          <Button
            onClick={() => (window.location.href = "tel:******-BMW-SECURITY")}
            variant="outline"
            className="border-red-500 text-red-600 hover:bg-red-500 hover:text-white px-8 py-3 text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            size="lg"
          >
            <Phone className="w-5 h-5 mr-2" />
            Security Hotline
          </Button>

          <Button
            onClick={() => navigate("/")}
            variant="ghost"
            className="text-[#4D4D4D] hover:text-[#0066B1] px-8 py-3 text-lg font-semibold transition-all duration-300"
            size="lg"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Return Home
          </Button>
        </div>

        {/* Support Information */}
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-3xl mx-auto">
          <h3 className="text-xl font-bold text-[#4D4D4D] mb-6">
            Security & Appeals Team
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Mail className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">
                Security Email
              </h4>
              <p className="text-sm text-slate-600"><EMAIL></p>
              <p className="text-xs text-slate-500 mt-1">
                Priority response within 4 hours
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <Phone className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">
                Security Hotline
              </h4>
              <p className="text-sm text-slate-600">1-800-BMW-SECURITY</p>
              <p className="text-xs text-slate-500 mt-1">24/7 Emergency Line</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center mx-auto mb-3">
                <FileText className="w-6 h-6 text-white" />
              </div>
              <h4 className="font-semibold text-[#4D4D4D] mb-2">
                Case Reference
              </h4>
              <p className="text-sm text-slate-600">Include Account Email</p>
              <p className="text-xs text-slate-500 mt-1">
                For faster processing
              </p>
            </div>
          </div>
        </div>

        {/* Premium Badge */}
        <div className="mt-12">
          <div className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-[#4D4D4D] rounded-full text-white font-semibold shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full mr-3 animate-pulse"></div>
            Secure Platform • Account Protection • 24/7 Security
          </div>
        </div>
      </div>
    </div>
  );
}



// import { SignOutButton } from "@clerk/nextjs";
// import { Button } from "@/components/ui/button";
// import { getCurrentDbUser } from "@/lib/auth";
// import { redirect } from "next/navigation";

// export default async function AccountSuspendedPage() {
//     const user = await getCurrentDbUser()
  
//     if(!user){
//       redirect("sign-in")
//     }
//   return (
//     <div className="container mx-auto py-16 text-center">
//       <div className="max-w-md mx-auto p-8 rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold text-red-600 mb-4">Account Suspended</h1>
        
//         <p className="mb-6 text-gray-700">
//           Your account has been suspended. This may be due to a violation of our terms of service
//           or suspicious activity detected on your account.
//         </p>
        
//         <p className="mb-8 text-gray-700">
//           If you believe this is an error, please contact our support team at 
//           <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
//             <EMAIL>
//           </a>
//         </p>
        
//         <SignOutButton>
//           <Button variant="outline" className="w-full">
//             Sign Out
//           </Button>
//         </SignOutButton>
//       </div>
//     </div>
//   );
// }