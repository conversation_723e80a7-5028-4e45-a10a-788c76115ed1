"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Shield, 
  Smartphone, 
  Clock, 
  Activity,
  AlertTriangle,
  Eye
} from "lucide-react";
import { securitySettingsSchema, type SecuritySettingsInput } from "@/lib/zod";
import { updateSecuritySettings, type ActionResult } from "@/app/actions/account";
import { UserSecurityInfo, UserAuditLogEntry } from "@/app/getData/account-settings";

interface SecuritySettingsProps {
  securityInfo: UserSecurityInfo | null;
  auditLogs: UserAuditLogEntry[];
  ssoProvider?: string | null;
}

export default function SecuritySettings({ securityInfo, auditLogs, ssoProvider }: SecuritySettingsProps) {
  const [isPending, startTransition] = useTransition();
  const [result, setResult] = useState<ActionResult | null>(null);

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { isDirty }
  } = useForm<SecuritySettingsInput>({
    resolver: zodResolver(securitySettingsSchema),
    defaultValues: {
      twoFactorEnabled: securityInfo?.twoFactorEnabled || false,
    }
  });

  const watchedValues = watch();

  const onSubmit = (data: SecuritySettingsInput) => {
    startTransition(async () => {
      try {
        const result = await updateSecuritySettings(data);
        setResult(result);
        
        if (result.success) {
          setTimeout(() => setResult(null), 3000);
        }
      } catch (error) {
        setResult({
          success: false,
          error: "A apărut o eroare neașteptată. Vă rugăm să încercați din nou."
        });
      }
    });
  };

  const handleSwitchChange = (field: keyof SecuritySettingsInput, value: boolean) => {
    setValue(field, value, { shouldDirty: true });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ro-RO', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const getActionIcon = (action: string) => {
    if (action.includes('login')) return <Activity className="h-3 w-3" />;
    if (action.includes('password')) return <Shield className="h-3 w-3" />;
    if (action.includes('profile')) return <Eye className="h-3 w-3" />;
    return <AlertCircle className="h-3 w-3" />;
  };

  const getActionVariant = (action: string): "default" | "secondary" | "destructive" | "outline" => {
    if (action.includes('login')) return "default";
    if (action.includes('password')) return "destructive";
    if (action.includes('security')) return "destructive";
    return "secondary";
  };

  return (
    <div className="space-y-6">
      {/* Security Settings Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Setări Securitate
          </CardTitle>
          <CardDescription>
            Gestionați setările de securitate pentru a vă proteja contul
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Success/Error Messages */}
            {result && (
              <Alert variant={result.success ? "default" : "destructive"}>
                {result.success ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <AlertCircle className="h-4 w-4" />
                )}
                <AlertDescription>
                  {result.success 
                    ? "Setările de securitate au fost actualizate cu succes!" 
                    : result.error
                  }
                </AlertDescription>
              </Alert>
            )}

            {/* Two-Factor Authentication */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div className="space-y-1">
                    <Label htmlFor="twoFactorEnabled" className="text-base font-medium">
                      Autentificare cu doi factori (2FA)
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Adăugați un nivel suplimentar de securitate la contul dvs.
                    </p>
                  </div>
                </div>
                <Switch
                  id="twoFactorEnabled"
                  checked={watchedValues.twoFactorEnabled}
                  onCheckedChange={(checked) => handleSwitchChange("twoFactorEnabled", checked)}
                />
              </div>

              {watchedValues.twoFactorEnabled && (
                <Alert>
                  <Smartphone className="h-4 w-4" />
                  <AlertDescription>
                    <strong>2FA Activat:</strong> Contul dvs. este protejat cu autentificare cu doi factori. 
                    Veți fi solicitat să introduceți un cod de verificare la fiecare autentificare.
                  </AlertDescription>
                </Alert>
              )}

              {!watchedValues.twoFactorEnabled && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Recomandare de securitate:</strong> Activați autentificarea cu doi factori 
                    pentru a vă proteja contul împotriva accesului neautorizat.
                  </AlertDescription>
                </Alert>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={isPending || !isDirty}
                className="bg-[#0066B1] hover:bg-[#004d85]"
              >
                {isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Se salvează...
                  </>
                ) : (
                  "Salvează setările"
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Account Security Overview */}
      {securityInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Prezentare Generală Securitate
            </CardTitle>
            <CardDescription>
              Informații despre activitatea și securitatea contului dvs.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Ultima autentificare</Label>
                <p className="text-sm text-muted-foreground">
                  {securityInfo.lastLoginAt 
                    ? formatDate(securityInfo.lastLoginAt)
                    : "Niciodată"
                  }
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Numărul total de autentificări</Label>
                <p className="text-sm text-muted-foreground">
                  {securityInfo.loginCount.toLocaleString('ro-RO')}
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  {ssoProvider ? "Autentificare" : "Ultima schimbare parolă"}
                </Label>
                <p className="text-sm text-muted-foreground">
                  {ssoProvider
                    ? `Gestionată de ${ssoProvider === 'microsoft' ? 'Microsoft' : ssoProvider === 'google' ? 'Google' : ssoProvider}`
                    : securityInfo.passwordChangedAt
                      ? formatDate(securityInfo.passwordChangedAt)
                      : "Niciodată"
                  }
                </p>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Status 2FA</Label>
                <Badge variant={securityInfo.twoFactorEnabled ? "default" : "destructive"}>
                  {securityInfo.twoFactorEnabled ? "Activat" : "Dezactivat"}
                </Badge>
              </div>
            </div>

            {securityInfo.loginAttempts > 0 && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Atenție:</strong> Au fost detectate {securityInfo.loginAttempts} încercări 
                  de autentificare eșuate recente pe contul dvs.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}

      {/* Recent Activity Log */}
      {/* <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Activitate Recentă
          </CardTitle>
          <CardDescription>
            Ultimele 10 acțiuni efectuate pe contul dvs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {auditLogs.length > 0 ? (
            <ScrollArea className="h-[300px] w-full">
              <div className="space-y-3">
                {auditLogs.map((log) => (
                  <div key={log.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                    <div className="flex-shrink-0 mt-1">
                      {getActionIcon(log.action)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <Badge variant={getActionVariant(log.action)} className="text-xs">
                          {log.action}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(log.createdAt)}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {log.entityType}: {log.entityId || 'N/A'}
                      </p>
                      {log.ipAddress && (
                        <p className="text-xs text-muted-foreground">
                          IP: {log.ipAddress}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Nu există activitate recentă înregistrată</p>
            </div>
          )}
        </CardContent>
      </Card> */}
    </div>
  );
}
