import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({ isSuspended: false });
    }

    // Check user suspension status in database
    const user = await prisma.user.findUnique({
      where: { externalId: userId },
      select: { 
        isSuspended: true, 
        isActive: true, 
        deletedAt: true,
        suspensionReason: true
      }
    });

    if (!user) {
      return NextResponse.json({ isSuspended: false });
    }

    const isSuspended = user.isSuspended || !user.isActive || user.deletedAt !== null;

    return NextResponse.json({ 
      isSuspended,
      reason: user.suspensionReason 
    });

  } catch (error) {
    logger.error('[check-suspension] Error checking suspension status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
