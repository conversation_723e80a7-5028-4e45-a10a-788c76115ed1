'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface SuspensionCheckProps {
  children: React.ReactNode;
}

export default function SuspensionCheck({ children }: SuspensionCheckProps) {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkSuspension = async () => {
      if (!isLoaded) return;
      
      if (!user) {
        setIsChecking(false);
        return;
      }

      try {
        // Check if user is suspended via API call
        const response = await fetch('/api/auth/check-suspension', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          
          if (data.isSuspended) {
            // Redirect to suspended page
            router.push('/account/suspended');
            return;
          }
        }
      } catch (error) {
        console.error('Error checking suspension status:', error);
        // Continue normally if check fails
      }

      setIsChecking(false);
    };

    checkSuspension();
  }, [user, isLoaded, router]);

  // Show loading while checking
  if (isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return <>{children}</>;
}
