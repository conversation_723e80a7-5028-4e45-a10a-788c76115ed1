"use client";

import { useState, useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, CheckCircle, AlertCircle } from "lucide-react";
import { profileUpdateSchema, type ProfileUpdateInput } from "@/lib/zod";
import { updateProfile, type ActionResult } from "@/app/actions/account";
import { AccountSettingsData } from "@/app/getData/account-settings";

interface ProfileFormProps {
  accountData: AccountSettingsData;
}

export default function ProfileForm({ accountData }: ProfileFormProps) {
  const [isPending, startTransition] = useTransition();
  const [result, setResult] = useState<ActionResult | null>(null);

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty }
  } = useForm<ProfileUpdateInput>({
    resolver: zodResolver(profileUpdateSchema),
    defaultValues: {
      firstName: accountData.firstName,
      lastName: accountData.lastName,
      email: accountData.email,
      phoneNumber: accountData.phoneNumber || "",
      bio: accountData.bio || "",
      jobTitle: accountData.jobTitle || "",
      department: accountData.department || "",
      salutation: accountData.salutation || undefined,
      preferredLanguage: accountData.preferredLanguage || "",
      timezone: accountData.timezone || "",
    }
  });

  const salutationValue = watch("salutation");

  const onSubmit = (data: ProfileUpdateInput) => {
    startTransition(async () => {
      try {
        const result = await updateProfile(data);
        setResult(result);
        
        if (result.success) {
          // Reset form dirty state on success
          setTimeout(() => setResult(null), 3000);
        }
      } catch (error) {
        setResult({
          success: false,
          error: "A apărut o eroare neașteptată. Vă rugăm să încercați din nou."
        });
      }
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informații Profil</CardTitle>
        <CardDescription>
          Actualizați informațiile dvs. personale și de contact
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Success/Error Messages */}
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {result.success 
                  ? "Profilul a fost actualizat cu succes!" 
                  : result.error
                }
              </AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Salutation */}
            <div className="space-y-2">
              <Label htmlFor="salutation">Titulatura</Label>
              <Select
                value={salutationValue || ""}
                onValueChange={(value) => setValue("salutation", value as "Dl" | "Dna", { shouldDirty: true })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selectați titulatura" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Dl">Dl.</SelectItem>
                  <SelectItem value="Dna">Dna.</SelectItem>
                </SelectContent>
              </Select>
              {errors.salutation && (
                <p className="text-sm text-red-600">{errors.salutation.message}</p>
              )}
            </div>

            {/* First Name */}
            <div className="space-y-2">
              <Label htmlFor="firstName">Prenume *</Label>
              <Input
                id="firstName"
                {...register("firstName")}
                className={errors.firstName ? "border-red-500" : ""}
              />
              {errors.firstName && (
                <p className="text-sm text-red-600">{errors.firstName.message}</p>
              )}
            </div>

            {/* Last Name */}
            <div className="space-y-2">
              <Label htmlFor="lastName">Nume de familie *</Label>
              <Input
                id="lastName"
                {...register("lastName")}
                className={errors.lastName ? "border-red-500" : ""}
              />
              {errors.lastName && (
                <p className="text-sm text-red-600">{errors.lastName.message}</p>
              )}
            </div>

            {/* Email */}
            <div className="space-y-2">
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                className={errors.email ? "border-red-500" : ""}
              />
              {errors.email && (
                <p className="text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            {/* Phone Number */}
            <div className="space-y-2">
              <Label htmlFor="phoneNumber">Telefon</Label>
              <Input
                id="phoneNumber"
                type="tel"
                placeholder="+40712345678"
                {...register("phoneNumber")}
                className={errors.phoneNumber ? "border-red-500" : ""}
              />
              {errors.phoneNumber && (
                <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
              )}
            </div>

            {/* Job Title */}
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Funcția</Label>
              <Input
                id="jobTitle"
                {...register("jobTitle")}
                className={errors.jobTitle ? "border-red-500" : ""}
              />
              {errors.jobTitle && (
                <p className="text-sm text-red-600">{errors.jobTitle.message}</p>
              )}
            </div>

            {/* Department */}
            <div className="space-y-2">
              <Label htmlFor="department">Departament</Label>
              <Input
                id="department"
                {...register("department")}
                className={errors.department ? "border-red-500" : ""}
              />
              {errors.department && (
                <p className="text-sm text-red-600">{errors.department.message}</p>
              )}
            </div>

            {/* Preferred Language */}
            <div className="space-y-2">
              <Label htmlFor="preferredLanguage">Limba preferată</Label>
              <Select
                value={watch("preferredLanguage") || ""}
                onValueChange={(value) => setValue("preferredLanguage", value, { shouldDirty: true })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selectați limba" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ro">Română</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="de">Deutsch</SelectItem>
                  <SelectItem value="fr">Français</SelectItem>
                </SelectContent>
              </Select>
              {errors.preferredLanguage && (
                <p className="text-sm text-red-600">{errors.preferredLanguage.message}</p>
              )}
            </div>

            {/* Timezone */}
            <div className="space-y-2">
              <Label htmlFor="timezone">Fusul orar</Label>
              <Select
                value={watch("timezone") || ""}
                onValueChange={(value) => setValue("timezone", value, { shouldDirty: true })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selectați fusul orar" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Europe/Bucharest">Europa/București</SelectItem>
                  <SelectItem value="Europe/London">Europa/Londra</SelectItem>
                  <SelectItem value="Europe/Berlin">Europa/Berlin</SelectItem>
                  <SelectItem value="America/New_York">America/New York</SelectItem>
                </SelectContent>
              </Select>
              {errors.timezone && (
                <p className="text-sm text-red-600">{errors.timezone.message}</p>
              )}
            </div>
          </div>

          {/* Bio - Full Width */}
          <div className="space-y-2">
            <Label htmlFor="bio">Biografie</Label>
            <Textarea
              id="bio"
              placeholder="Scrieți câteva cuvinte despre dvs..."
              rows={4}
              {...register("bio")}
              className={errors.bio ? "border-red-500" : ""}
            />
            {errors.bio && (
              <p className="text-sm text-red-600">{errors.bio.message}</p>
            )}
          </div>

          {/* Field Errors */}
          {result?.fieldErrors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {Object.entries(result.fieldErrors).map(([field, errors]) => (
                    <li key={field}>
                      <strong>{field}:</strong> {errors.join(", ")}
                    </li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isPending || !isDirty}
              className="bg-[#0066B1] hover:bg-[#004d85]"
            >
              {isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Se salvează...
                </>
              ) : (
                "Salvează modificările"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
