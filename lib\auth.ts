
import { auth, currentUser } from "@clerk/nextjs/server";
import { cache } from "react";
import { findOrCreateUser } from "./sync-user";
import { shouldUpdateLoginStats } from "./session";
import { logger } from "./logger";

/**
 * Checks if a Clerk user signed up with SSO (Social Sign-On)
 * Returns the SSO provider name if user signed up with SSO, null otherwise
 */
export const getUserSSOProvider = cache(async (): Promise<string | null> => {
  try {
    const clerkUser = await currentUser();

    if (!clerkUser || !clerkUser.externalAccounts) {
      return null;
    }

    // Check if user has any external accounts (SSO)
    const ssoAccount = clerkUser.externalAccounts.find(account =>
      ['google', 'microsoft', 'facebook', 'github', 'apple'].includes(account.provider)
    );

    return ssoAccount ? ssoAccount.provider : null;
  } catch (error) {
    logger.error('[getUserSSOProvider] Error checking SSO provider:', error);
    return null;
  }
});


/**
 * Gets the current authenticated user from our database
 * This syncs the Clerk user with our database if needed
 * Uses React's cache() to deduplicate requests within a render cycle
 */
export const getCurrentDbUser = cache(async () => {
  const { userId } = await auth();

  if (!userId) {
    logger.warn('[getCurrentDbUser] No userId found in auth()');
    return null;
  }

  try {
    // Add timeout to Clerk API calls - GOOD PRACTICE
    const clerkUser = await Promise.race([      
      currentUser(),
      new Promise<null>((_, reject) => 
        setTimeout(() => reject(new Error('Clerk API timeout')), 10000)
      )
    ]);

    if (!clerkUser) {
      logger.warn(`[getCurrentDbUser] Clerk user not found for userId: ${userId}`);
      return null;
    }

    // ✅ FIX: Remove userId parameter
    //const shouldUpdate = await shouldUpdateLoginStats(userId);

    const dbUser = await findOrCreateUser({
      externalId: userId,
      email: clerkUser.emailAddresses[0]?.emailAddress || '',
      firstName: clerkUser.firstName || '',
      lastName: clerkUser.lastName || '',
      profileImage: clerkUser.imageUrl || '',
      updateLoginStats: false//shouldUpdate,
    });

    if (!dbUser) {
      logger.error(`[getCurrentDbUser] Failed to find or create user for externalId: ${userId}`);
      return null;
    }

    if (!dbUser.isActive) {
      logger.warn(`[getCurrentDbUser] User is inactive: ${userId}`);
      return null;
    }

    if (dbUser.isSuspended) {
      logger.warn(`[getCurrentDbUser] User is suspended: ${userId}`);
      return null;
    }

    return dbUser;
  } catch (error) {
    logger.error('[getCurrentDbUser] Error fetching or creating user:', error);
    
    // Handle timeout specifically
    if (error instanceof Error && error.message === 'Clerk API timeout') {
      logger.error('[getCurrentDbUser] Clerk API timed out - this may indicate network issues');
    }
    
    return null;
  }
});