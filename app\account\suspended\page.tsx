'use client';

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuth, useClerk } from "@clerk/nextjs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Pause, AlertTriangle, Mail, Phone } from "lucide-react";

export default function AccountSuspendedPage() {
  const { signOut } = useClerk();
  const { isLoaded } = useAuth();
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Sign out error:', error);
      // Force redirect even if sign out fails
      window.location.href = '/';
    }
  };

  const handleContactSupport = () => {
    // You can customize this with your actual support contact method
    window.location.href = 'mailto:<EMAIL>?subject=Reactivare Cont Suspendat';
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
            <Pause className="h-6 w-6 text-orange-600" />
          </div>
          <CardTitle className="text-orange-600">Cont Suspendat</CardTitle>
          <CardDescription>
            Contul dvs. a fost suspendat temporar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Contul dvs. este momentan suspendat.</strong><br />
              Acest lucru înseamnă că nu puteți accesa funcționalitățile platformei în acest moment.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <h4 className="font-medium">Ce puteți face:</h4>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li className="flex items-start space-x-2">
                <Mail className="h-4 w-4 mt-0.5 text-blue-500" />
                <span>Contactați echipa de suport pentru clarificări</span>
              </li>
              <li className="flex items-start space-x-2">
                <Phone className="h-4 w-4 mt-0.5 text-green-500" />
                <span>Solicitați reactivarea contului dacă considerați că suspendarea este greșită</span>
              </li>
            </ul>
          </div>

          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Important:</strong> Încercarea repetată de autentificare nu va rezolva problema. 
              Vă rugăm să contactați suportul pentru asistență.
            </AlertDescription>
          </Alert>

          <div className="space-y-3">
            <Button 
              onClick={handleContactSupport}
              className="w-full"
            >
              <Mail className="h-4 w-4 mr-2" />
              Contactează Suportul
            </Button>

            <Button 
              variant="outline"
              onClick={handleSignOut}
              disabled={isSigningOut}
              className="w-full"
            >
              {isSigningOut ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                  Se deconectează...
                </>
              ) : (
                'Deconectează-te'
              )}
            </Button>
          </div>

          <div className="text-center text-sm text-muted-foreground">
            <p>Pentru întrebări urgente:</p>
            <p className="font-medium"><EMAIL></p>
            <p className="font-medium">+40 XXX XXX XXX</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
