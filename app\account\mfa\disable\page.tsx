'use client';

import { useUser } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Shield, AlertTriangle, CheckCircle, AlertCircle } from "lucide-react";

export default function MFADisablePage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [isDisabling, setIsDisabling] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  // useEffect(() => {
  //   if (isLoaded && !user) {
  //     router.push('/sign-in');
  //   }
  // }, [isLoaded, user, router]);

  const handleDisableMFA = async () => {
    if (!user || !verificationCode.trim()) {
      setError('Vă rugăm să introduceți codul de verificare.');
      return;
    }

    setIsDisabling(true);
    setError(null);

    try {
      // Get the user's TOTP factors
      const totpFactors = user.twoFactorEnabled ? user.twoFactorEnabled : [];
      
      if (totpFactors.length === 0) {
        setError('Nu există factori 2FA configurați pe acest cont.');
        return;
      }

      // Verify the code first
      const factor = totpFactors[0];
      await factor.attemptVerification({ code: verificationCode });

      // If verification succeeds, disable the factor
      await factor.destroy();
      
      setSuccess(true);
      
      // Redirect back to security settings after a delay
      setTimeout(() => {
        window.close(); // Close the popup window
        // Or redirect to security settings if not in popup
        router.push('/account/settings?tab=security');
      }, 3000);
      
    } catch (err: any) {
      console.error('MFA disable error:', err);
      if (err.message?.includes('verification')) {
        setError('Codul de verificare este incorect. Vă rugăm să încercați din nou.');
      } else {
        setError(err.message || 'A apărut o eroare la dezactivarea 2FA. Vă rugăm să încercați din nou.');
      }
    } finally {
      setIsDisabling(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle>Dezactivare Autentificare 2FA</CardTitle>
          <CardDescription>
            Confirmați dezactivarea autentificării cu doi factori
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                2FA a fost dezactivat cu succes! Veți fi redirecționat înapoi la setări.
              </AlertDescription>
            </Alert>
          )}

          {!success && !showConfirmation && (
            <>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Atenție:</strong> Dezactivarea 2FA va reduce securitatea contului dvs. 
                  Recomandăm să mențineți această funcție activată.
                </AlertDescription>
              </Alert>

              <Button 
                variant="destructive"
                onClick={() => setShowConfirmation(true)}
                className="w-full"
              >
                Continuă cu dezactivarea
              </Button>
            </>
          )}

          {!success && showConfirmation && (
            <>
              <div className="space-y-3">
                <Label htmlFor="verification-code">
                  Cod de verificare din aplicația de autentificare
                </Label>
                <Input
                  id="verification-code"
                  type="text"
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                />
                <p className="text-sm text-muted-foreground">
                  Introduceți codul din aplicația dvs. de autentificare pentru a confirma dezactivarea.
                </p>
              </div>

              <Button 
                variant="destructive"
                onClick={handleDisableMFA}
                disabled={isDisabling || !verificationCode.trim()}
                className="w-full"
              >
                {isDisabling ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Se dezactivează...
                  </>
                ) : (
                  'Dezactivează 2FA'
                )}
              </Button>
            </>
          )}

          <Button 
            variant="outline" 
            onClick={() => {
              if (showConfirmation) {
                setShowConfirmation(false);
              } else {
                window.close();
                router.push('/account/settings?tab=security');
              }
            }}
            className="w-full"
          >
            {showConfirmation ? 'Înapoi' : 'Anulează'}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
